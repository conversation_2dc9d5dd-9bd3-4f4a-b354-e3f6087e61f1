import os
import subprocess
import sys

def create_file(path, content):
    """創建文件並寫入內容"""
    directory = os.path.dirname(path)
    if directory:
        os.makedirs(directory, exist_ok=True)
    
    with open(path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"✅ 創建文件: {path}")

def run_command(command, cwd=None):
    """執行命令"""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 執行成功: {command}")
            return True
        else:
            print(f"❌ 執行失敗: {command}")
            print(f"錯誤: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 執行異常: {e}")
        return False

print("🚀 開始自動部署台指期貨交易決策系統...")

# 創建項目目錄
project_name = "taiwan_futures_system"
print(f"📁 創建項目目錄: {project_name}")
os.makedirs(project_name, exist_ok=True)
os.makedirs(f"{project_name}/templates", exist_ok=True)
os.makedirs(f"{project_name}/static", exist_ok=True)

# 創建 app.py
app_py_content = '''from flask import Flask, render_template, jsonify
from flask_socketio import SocketIO, emit
import threading
import time
from datetime import datetime
import random

app = Flask(__name__)
app.config['SECRET_KEY'] = 'taiwan-futures-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

class TaiwanIndexTracker:
    def __init__(self):
        self.current_spot_price = 22739.0
        self.current_futures_price = 22825.74
        self.signal = "觀望"
        self.spread = 0
        self.volume = 4821
        
    def simulate_market_data(self):
        """模擬市場數據變化"""
        change = random.uniform(-50, 50)
        self.current_spot_price += change
        self.current_spot_price = round(self.current_spot_price, 2)
        
        futures_change = change + random.uniform(-20, 20)
        self.current_futures_price += futures_change
        self.current_futures_price = round(self.current_futures_price, 2)
        
        self.volume += random.randint(-100, 100)
        if self.volume < 1000:
            self.volume = 1000
            
    def calculate_signal(self):
        """計算交易訊號"""
        self.spread = round(self.current_futures_price - self.current_spot_price, 2)
        
        if self.spread < -15:
            self.signal = "做多"
        elif self.spread > 15:
            self.signal = "做空"
        else:
            self.signal = "觀望"
            
    def update_data(self):
        """更新所有數據"""
        self.simulate_market_data()
        self.calculate_signal()

tracker = TaiwanIndexTracker()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    return jsonify({
        'spot_price': tracker.current_spot_price,
        'futures_price': tracker.current_futures_price,
        'spread': tracker.spread,
        'signal': tracker.signal,
        'volume': tracker.volume,
        'timestamp': datetime.now().strftime('%H:%M:%S')
    })

@socketio.on('connect')
def handle_connect():
    print('客戶端已連接')
    emit('status', {'msg': '已連接到台指期貨系統'})

def background_update():
    """背景更新數據"""
    while True:
        tracker.update_data()
        socketio.emit('data_update', {
            'spot_price': tracker.current_spot_price,
            'futures_price': tracker.current_futures_price,
            'spread': tracker.spread,
            'signal': tracker.signal,
            'volume': tracker.volume,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })
        time.sleep(3)

if __name__ == '__main__':
    print("🚀 啟動台指期貨交易決策系統...")
    print("📊 系統將在 http://localhost:5000 運行")
    
    update_thread = threading.Thread(target=background_update)
    update_thread.daemon = True
    update_thread.start()
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
'''

# 創建 HTML 模板
html_content = '''<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐂 台指期貨交易決策系統</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐂 台指期貨交易決策系統</h1>
            <div class="status" id="connection-status">● 連線中</div>
        </div>
        
        <div class="main-panel">
            <div class="price-display">
                <div class="price-box spot">
                    <h3>現貨預估</h3>
                    <div id="spot-price" class="price">22739</div>
                </div>
                
                <div class="spread-box">
                    <h3>價差</h3>
                    <div id="spread" class="spread">+86.74</div>
                    <div class="spread-desc">期貨-現貨</div>
                </div>
                
                <div class="price-box futures">
                    <h3>期貨價格</h3>
                    <div id="futures-price" class="price">22825.74</div>
                </div>
            </div>
            
            <div class="signal-panel">
                <div id="signal" class="signal-box 觀望">觀望</div>
                <div class="signal-bars">
                    <div class="bar" id="bar1"></div>
                    <div class="bar" id="bar2"></div>
                    <div class="bar" id="bar3"></div>
                    <div class="bar" id="bar4"></div>
                    <div class="bar" id="bar5"></div>
                </div>
                <div class="signal-desc">訊號強度</div>
            </div>
            
            <div class="info-panel">
                <div class="info-item">
                    <span class="label">時間:</span>
                    <span id="timestamp">09:06:06</span>
                </div>
                <div class="info-item">
                    <span class="label">現貨預估量:</span>
                    <span id="volume">4821</span>
                </div>
                <div class="info-item">
                    <span class="label">策略:</span>
                    <span>價差套利</span>
                </div>
            </div>
            
            <div class="rules-panel">
                <h4>📋 交易規則</h4>
                <div class="rule">🟢 做多條件: 期貨價格低於現貨15點以上</div>
                <div class="rule">🔴 做空條件: 期貨價格高於現貨15點以上</div>
                <div class="rule">⚪ 觀望條件: 價差在±15點以內</div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>'''

# 創建 CSS 樣式
css_content = '''* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft JhengHei', Arial, sans-serif;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 900px;
    margin: 0 auto;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: #00ff88;
    font-size: 28px;
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.status {
    color: #00ff88;
    font-size: 14px;
}

.main-panel {
    background: rgba(45, 45, 45, 0.9);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.price-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 15px;
}

.price-box, .spread-box {
    flex: 1;
    text-align: center;
    padding: 20px;
    background: rgba(61, 61, 61, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.price-box h3, .spread-box h3 {
    font-size: 14px;
    color: #ccc;
    margin-bottom: 10px;
}

.price {
    font-size: 28px;
    font-weight: bold;
    color: #00ff88;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.spread {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.spread.positive { color: #ff6b6b; }
.spread.negative { color: #51cf66; }
.spread.neutral { color: #ffd43b; }

.spread-desc {
    font-size: 12px;
    color: #999;
}

.signal-panel {
    text-align: center;
    margin-bottom: 25px;
}

.signal-box {
    font-size: 42px;
    font-weight: bold;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.signal-box.做多 {
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    color: white;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
}

.signal-box.做空 {
    background: linear-gradient(135deg, #51cf66, #4caf50);
    color: white;
    box-shadow: 0 0 20px rgba(81, 207, 102, 0.4);
}

.signal-box.觀望 {
    background: linear-gradient(135deg, #666, #555);
    color: white;
    box-shadow: 0 0 20px rgba(102, 102, 102, 0.2);
}

.signal-bars {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 10px;
}

.bar {
    width: 50px;
    height: 25px;
    background: #444;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.bar.active-long { 
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

.bar.active-short { 
    background: linear-gradient(135deg, #51cf66, #4caf50);
    box-shadow: 0 0 10px rgba(81, 207, 102, 0.5);
}

.signal-desc {
    font-size: 12px;
    color: #999;
}

.info-panel {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.info-item {
    background: rgba(61, 61, 61, 0.6);
    padding: 12px 18px;
    border-radius: 8px;
    font-size: 14px;
}

.label {
    color: #ccc;
    margin-right: 8px;
}

.rules-panel {
    background: rgba(61, 61, 61, 0.6);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #00ff88;
}

.rules-panel h4 {
    color: #00ff88;
    margin-bottom: 15px;
    font-size: 16px;
}

.rule {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .price-display {
        flex-direction: column;
    }
    
    .info-panel {
        flex-direction: column;
    }
    
    .signal-box {
        font-size: 32px;
        padding: 20px;
    }
}'''

# 創建 JavaScript
js_content = '''const socket = io();

socket.on('connect', function() {
    console.log('✅ 已連接到台指期貨系統');
    document.getElementById('connection-status').textContent = '● 已連線';
    document.getElementById('connection-status').style.color = '#00ff88';
});

socket.on('disconnect', function() {
    console.log('❌ 與伺服器斷線');
    document.getElementById('connection-status').textContent = '● 斷線';
    document.getElementById('connection-status').style.color = '#ff6b6b';
});

socket.on('data_update', function(data) {
    updateDisplay(data);
});

function updateDisplay(data) {
    document.getElementById('spot-price').textContent = data.spot_price.toLocaleString();
    document.getElementById('futures-price').textContent = data.futures_price.toLocaleString();
    document.getElementById('timestamp').textContent = data.timestamp;
    document.getElementById('volume').textContent = data.volume.toLocaleString();
    
    updateSpread(data.spread);
    updateSignal(data.signal);
    updateSignalBars(data.spread);
}

function updateSpread(spread) {
    const spreadElement = document.getElementById('spread');
    const formattedSpread = spread > 0 ? `+${spread}` : spread.toString();
    spreadElement.textContent = formattedSpread;
    
    spreadElement.className = 'spread';
    if (spread > 5) {
        spreadElement.classList.add('positive');
    } else if (spread < -5) {
        spreadElement.classList.add('negative');
    } else {
        spreadElement.classList.add('neutral');
    }
}

function updateSignal(signal) {
    const signalElement = document.getElementById('signal');
    signalElement.textContent = signal;
    signalElement.className = `signal-box ${signal}`;
    
    if (signalElement.dataset.lastSignal !== signal) {
        signalElement.style.transform = 'scale(1.05)';
        setTimeout(() => {
            signalElement.style.transform = 'scale(1)';
        }, 200);
        signalElement.dataset.lastSignal = signal;
    }
}

function updateSignalBars(spread) {
    const bars = document.querySelectorAll('.bar');
    
    bars.forEach(bar => {
        bar.className = 'bar';
    });
    
    const strength = Math.min(Math.abs(spread) / 30, 1);
    const activeBars = Math.ceil(strength * 5);
    
    for (let i = 0; i < activeBars; i++) {
        if (spread > 0) {
            bars[i].classList.add('active-short');
        } else {
            bars[i].classList.add('active-long');
        }
    }
}

fetch('/api/data')
    .then(response => response.json())
    .then(data => {
        console.log('📊 初始數據載入:', data);
        updateDisplay(data);
    })
    .catch(error => {
        console.error('❌ 載入數據失敗:', error);
    });

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 台指期貨交易決策系統已啟動');
});'''

# 創建 requirements.txt
requirements_content = '''Flask==2.3.3
Flask-SocketIO==5.3.6
python-socketio==5.8.0
python-engineio==4.7.1'''

# 創建啟動腳本
start_script = '''@echo off
chcp 65001
echo 🚀 啟動台指期貨交易決策系統...
echo.
echo 📦 安裝依賴套件...
pip install -r requirements.txt
echo.
echo 🌐 啟動網頁伺服器...
echo 📊 請在瀏覽器中訪問: http://localhost:5000
echo.
python app.py
pause'''

# 創建所有文件
print("📝 創建應用程式文件...")
create_file(f"{project_name}/app.py", app_py_content)
create_file(f"{project_name}/templates/index.html", html_content)
create_file(f"{project_name}/static/style.css", css_content)
create_file(f"{project_name}/static/app.js", js_content)
create_file(f"{project_name}/requirements.txt", requirements_content)
create_file(f"{project_name}/start.bat", start_script)

# 自動安裝依賴
print("\n📦 安裝 Python 依賴套件...")
if run_command("pip install Flask Flask-SocketIO python-socketio python-engineio", cwd=project_name):
    print("✅ 依賴套件安裝成功！")
else:
    print("⚠️ 依賴套件安裝失敗，請手動執行: pip install -r requirements.txt")

# 完成提示
print(f"\n🎉 台指期貨交易決策系統部署完成！")
print(f"📁 項目路徑: {os.path.abspath(project_name)}")
print(f"\n🚀 啟動方式:")
print(f"方式1 - 快速啟動:")
print(f"   雙擊 {project_name}/start.bat")
print(f"\n方式2 - 命令行啟動:")
print(f"   cd {project_name}")
print(f"   python app.py")
print(f"\n🌐 啟動後請訪問: http://localhost:5000")

# 詢問是否立即啟動
try:
    choice = input("\n❓ 是否立即啟動系統？(y/n): ").lower().strip()
    if choice in ['y', 'yes', '是', '']:
        print("\n🚀 正在啟動系統...")
        os.chdir(project_name)
        subprocess.run([sys.executable, "app.py"])
except KeyboardInterrupt:
    print("\n👋 部署完成，您可以稍後手動啟動系統！")