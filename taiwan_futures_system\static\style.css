* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft JhengHei', Arial, sans-serif;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 900px;
    margin: 0 auto;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: #00ff88;
    font-size: 28px;
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.status {
    color: #00ff88;
    font-size: 14px;
}

.main-panel {
    background: rgba(45, 45, 45, 0.9);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.price-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 15px;
}

.price-box, .spread-box {
    flex: 1;
    text-align: center;
    padding: 20px;
    background: rgba(61, 61, 61, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.price-box h3, .spread-box h3 {
    font-size: 14px;
    color: #ccc;
    margin-bottom: 10px;
}

.price {
    font-size: 28px;
    font-weight: bold;
    color: #00ff88;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.spread {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.spread.positive { color: #ff6b6b; }
.spread.negative { color: #51cf66; }
.spread.neutral { color: #ffd43b; }

.spread-desc {
    font-size: 12px;
    color: #999;
}

.signal-panel {
    text-align: center;
    margin-bottom: 25px;
}

.signal-box {
    font-size: 42px;
    font-weight: bold;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.signal-box.做多 {
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    color: white;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
}

.signal-box.做空 {
    background: linear-gradient(135deg, #51cf66, #4caf50);
    color: white;
    box-shadow: 0 0 20px rgba(81, 207, 102, 0.4);
}

.signal-box.觀望 {
    background: linear-gradient(135deg, #666, #555);
    color: white;
    box-shadow: 0 0 20px rgba(102, 102, 102, 0.2);
}

.signal-bars {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 10px;
}

.bar {
    width: 50px;
    height: 25px;
    background: #444;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.bar.active-long { 
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

.bar.active-short { 
    background: linear-gradient(135deg, #51cf66, #4caf50);
    box-shadow: 0 0 10px rgba(81, 207, 102, 0.5);
}

.signal-desc {
    font-size: 12px;
    color: #999;
}

.info-panel {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.info-item {
    background: rgba(61, 61, 61, 0.6);
    padding: 12px 18px;
    border-radius: 8px;
    font-size: 14px;
}

.label {
    color: #ccc;
    margin-right: 8px;
}

.rules-panel {
    background: rgba(61, 61, 61, 0.6);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #00ff88;
}

.rules-panel h4 {
    color: #00ff88;
    margin-bottom: 15px;
    font-size: 16px;
}

.rule {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

.data-source {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.data-source h4 {
    color: #00ff88;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: normal;
}

.data-source .source-name {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.data-source.yahoo { 
    border-color: #00ff88; 
    background: rgba(0, 255, 136, 0.15);
}

.data-source.finmind { 
    border-color: #ffd43b; 
    background: rgba(255, 212, 59, 0.15);
}

.data-source.simulation { 
    border-color: #999; 
    background: rgba(153, 153, 153, 0.15);
}

@media (max-width: 768px) {
    .price-display {
        flex-direction: column;
    }
    
    .info-panel {
        flex-direction: column;
    }
    
    .signal-box {
        font-size: 32px;
        padding: 20px;
    }
}
