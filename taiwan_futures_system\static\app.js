const socket = io();

socket.on('connect', function() {
    console.log('✅ 已連接到台指期貨系統');
    document.getElementById('connection-status').textContent = '● 已連線';
    document.getElementById('connection-status').style.color = '#00ff88';
});

socket.on('disconnect', function() {
    console.log('❌ 與伺服器斷線');
    document.getElementById('connection-status').textContent = '● 斷線';
    document.getElementById('connection-status').style.color = '#ff6b6b';
});

socket.on('data_update', function(data) {
    updateDisplay(data);
});

function updateDisplay(data) {
    document.getElementById('spot-price').textContent = data.spot_price.toLocaleString();
    document.getElementById('futures-price').textContent = data.futures_price.toLocaleString();
    document.getElementById('timestamp').textContent = data.timestamp;
    document.getElementById('volume').textContent = data.volume.toLocaleString();
    
    // 更新數據來源顯示
    updateDataSource(data.data_source || "未知數據源");
    
    updateSpread(data.spread);
    updateSignal(data.signal);
    updateSignalBars(data.spread);
}

function updateSpread(spread) {
    const spreadElement = document.getElementById('spread');
    const formattedSpread = spread > 0 ? `+${spread}` : spread.toString();
    spreadElement.textContent = formattedSpread;
    
    spreadElement.className = 'spread';
    if (spread > 5) {
        spreadElement.classList.add('positive');
    } else if (spread < -5) {
        spreadElement.classList.add('negative');
    } else {
        spreadElement.classList.add('neutral');
    }
}

function updateSignal(signal) {
    const signalElement = document.getElementById('signal');
    signalElement.textContent = signal;
    signalElement.className = `signal-box ${signal}`;
    
    if (signalElement.dataset.lastSignal !== signal) {
        signalElement.style.transform = 'scale(1.05)';
        setTimeout(() => {
            signalElement.style.transform = 'scale(1)';
        }, 200);
        signalElement.dataset.lastSignal = signal;
    }
}

function updateSignalBars(spread) {
    const bars = document.querySelectorAll('.bar');
    
    bars.forEach(bar => {
        bar.className = 'bar';
    });
    
    const strength = Math.min(Math.abs(spread) / 30, 1);
    const activeBars = Math.ceil(strength * 5);
    
    for (let i = 0; i < activeBars; i++) {
        if (spread > 0) {
            bars[i].classList.add('active-short');
        } else {
            bars[i].classList.add('active-long');
        }
    }
}

function updateDataSource(source) {
    const sourcePanel = document.getElementById('data-source-panel');
    const sourceName = document.getElementById('data-source-name');
    
    if (sourcePanel && sourceName) {
        sourceName.textContent = source;
        
        // 移除所有樣式類別
        sourcePanel.className = 'data-source';
        
        // 根據數據源添加對應樣式
        if (source.includes('Yahoo Finance')) {
            sourcePanel.classList.add('yahoo');
        } else if (source.includes('FinMind')) {
            sourcePanel.classList.add('finmind');
        } else {
            sourcePanel.classList.add('simulation');
        }
        
        console.log(`📊 數據來源更新: ${source}`);
    }
}

fetch('/api/data')
    .then(response => response.json())
    .then(data => {
        console.log('📊 初始數據載入:', data);
        updateDisplay(data);
    })
    .catch(error => {
        console.error('❌ 載入數據失敗:', error);
    });

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 台指期貨交易決策系統已啟動');
});
