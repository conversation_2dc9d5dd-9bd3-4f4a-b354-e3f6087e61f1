from flask import Flask, render_template, jsonify
from flask_socketio import <PERSON>cket<PERSON>, emit
import threading
import time
from datetime import datetime
import random
import requests
import yfinance as yf

app = Flask(__name__)
app.config['SECRET_KEY'] = 'taiwan-futures-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

class TaiwanIndexTracker:
    def __init__(self):
        self.current_spot_price = 22739.0
        self.current_futures_price = 22825.74
        self.signal = "觀望"
        self.spread = 0
        self.volume = 4821
        self.data_source = "初始化中..."
        
    def get_finmind_data(self):
        """FinMind - 第一優先數據源"""
        try:
            api_key = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************.96WtyMrEsEqVAuv-0s9LntqIXrCbp5Gkc_6G7vqLRnY"
            
            # 使用今天的日期
            today = datetime.now().strftime('%Y-%m-%d')
            
            url = "https://api.finmindtrade.com/api/v4/data"
            params = {
                "dataset": "TaiwanVariousIndicators5Seconds",
                "data_id": "加權指數", 
                "start_date": today,
                "end_date": today,
                "token": api_key
            }
            response = requests.get(url, params=params, timeout=10)
            print(f"FinMind API 狀態: {response.status_code}")
            print(f"查詢日期: {today}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"FinMind 回應: {data}")
                
                if data.get('data') and len(data['data']) > 0:
                    latest = data['data'][-1]
                    self.current_spot_price = float(latest['price'])
                    self.data_source = "FinMind (加權指數5秒)"
                    print(f"✅ FinMind 加權指數獲取成功: {self.current_spot_price}")
                    return True
                else:
                    print("⚠️ FinMind 加權指數無數據返回")
            return False
        except Exception as e:
            print(f"❌ FinMind 加權指數失敗: {e}")
            return False

    def get_yahoo_data(self):
        """Yahoo Finance - 備援數據源"""
        try:
            # 使用台灣加權指數代碼
            ticker = yf.Ticker("^TWII")
            data = ticker.history(period="5d", interval="1d")  # 改為5天日線數據
            
            if not data.empty:
                latest_price = float(data['Close'].iloc[-1])
                self.current_spot_price = round(latest_price, 2)
                self.data_source = "Yahoo Finance (備援)"
                print(f"✅ Yahoo Finance 備援數據獲取成功: {self.current_spot_price}")
                return True
            else:
                print("⚠️ Yahoo Finance 無數據")
                return False
        except Exception as e:
            print(f"❌ Yahoo Finance 失敗: {e}")
            return False

    def get_alphavantage_data(self):
        """Alpha Vantage - 第二備源"""
        try:
            self.data_source = "Alpha Vantage (備源2)"
            return False
        except Exception as e:
            print(f"❌ Alpha Vantage 失敗: {e}")
            return False
    
    def simulate_market_data(self):
        """模擬數據 - 最終備源"""
        change = random.uniform(-50, 50)
        self.current_spot_price += change
        self.current_spot_price = round(self.current_spot_price, 2)
        
        futures_change = change + random.uniform(-20, 20)
        self.current_futures_price += futures_change
        self.current_futures_price = round(self.current_futures_price, 2)
        
        self.volume += random.randint(-100, 100)
        if self.volume < 1000:
            self.volume = 1000
            
        self.data_source = "模擬數據 (最終備源)"
            
    def calculate_signal(self):
        """計算交易訊號"""
        self.spread = round(self.current_futures_price - self.current_spot_price, 2)
        
        if self.spread < -15:
            self.signal = "做多"
        elif self.spread > 15:
            self.signal = "做空"
        else:
            self.signal = "觀望"
            
    def update_data(self):
        """多重數據源更新邏輯 - FinMind 優先"""
        if self.get_finmind_data():
            print("✅ 使用 FinMind 數據")
        elif self.get_yahoo_data():
            print("⚠️ FinMind 失敗，使用 Yahoo Finance 備援")
        elif self.get_alphavantage_data():
            print("⚠️ 前兩個數據源失敗，使用 Alpha Vantage 數據")
        else:
            print("❌ 所有外部數據源失敗，使用模擬數據")
            self.simulate_market_data()
        
        futures_change = random.uniform(-20, 20)
        self.current_futures_price += futures_change
        self.current_futures_price = round(self.current_futures_price, 2)
        
        self.calculate_signal()

tracker = TaiwanIndexTracker()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    data = {
        'spot_price': tracker.current_spot_price,
        'futures_price': tracker.current_futures_price,
        'spread': tracker.spread,
        'signal': tracker.signal,
        'volume': tracker.volume,
        'data_source': tracker.data_source,
        'timestamp': datetime.now().strftime('%H:%M:%S')
    }
    print(f"📊 API 回傳數據: {data}")
    return jsonify(data)

@socketio.on('connect')
def handle_connect():
    print('客戶端已連接')
    emit('status', {'msg': '已連接到台指期貨系統'})

def background_update():
    """背景更新數據"""
    while True:
        tracker.update_data()
        socketio.emit('data_update', {
            'spot_price': tracker.current_spot_price,
            'futures_price': tracker.current_futures_price,
            'spread': tracker.spread,
            'signal': tracker.signal,
            'volume': tracker.volume,
            'data_source': tracker.data_source,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })
        time.sleep(3)

if __name__ == '__main__':
    print("🚀 啟動台指期貨交易決策系統...")
    print("📊 系統將在 http://localhost:5000 運行")
    
    update_thread = threading.Thread(target=background_update)
    update_thread.daemon = True
    update_thread.start()
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
